<?php

namespace Modules\Core\Helpers;

use Random\RandomException;

class Helper
{
    public static function isBase64($string): bool
    {
        if (! is_string($string) || empty($string)) {
            return false;
        }

        if (! preg_match('/^[A-Za-z0-9+\/=]+$/', $string)) {
            return false;
        }

        if (strlen($string) % 4 !== 0) {
            return false;
        }

        $decoded = base64_decode($string, true);
        if ($decoded === false) {
            return false;
        }

        return base64_encode($decoded) === $string;
    }

    /**
     * @throws RandomException
     */
    public static function generatePasswordForRule($length = 16): string
    {
        $chars = [
            'lowercase' => 'abcdefghijklmnopqrstuvwxyz',
            'uppercase' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'numbers' => '0123456789',
            'symbols' => '!@#$%^&*()_+-=[]{}|;:,.<>?',
        ];

        $password = '';
        // One lowercase, one uppercase, one number, and one symbol
        $password .= $chars['lowercase'][random_int(0, strlen($chars['lowercase']) - 1)];
        $password .= $chars['uppercase'][random_int(0, strlen($chars['uppercase']) - 1)];
        $password .= $chars['numbers'][random_int(0, strlen($chars['numbers']) - 1)];
        $password .= $chars['symbols'][random_int(0, strlen($chars['symbols']) - 1)];

        // Add random characters until the desired length is reached
        $allChars = $chars['lowercase'].$chars['uppercase'].$chars['numbers'].$chars['symbols'];
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        return str_shuffle($password);
    }
}
