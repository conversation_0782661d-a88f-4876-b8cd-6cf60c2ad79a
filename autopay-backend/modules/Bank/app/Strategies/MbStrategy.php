<?php

namespace Modules\Bank\Strategies;

use App\Settings\BankSettings;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Bank\Contracts\BankStrategyInterface;

/**
 * MBBank Strategy Implementation
 *
 * Implements bank operations for MBBank using their API with RSA signature
 */
class MbStrategy implements BankStrategyInterface
{
    protected array $config;

    protected BankSettings $bankSettings;

    protected string $baseUrl;

    protected string $mbPublicKey;

    protected string $autoPayPublicKey;

    protected string $autoPayPrivateKey;

    protected string $authToken;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->bankSettings = app(BankSettings::class);

        // Initialize MBBank API configuration
        $apiConfig = $config['api_config'] ?? config('bank.supported_banks.mb.api_config', []);
        $this->baseUrl = $apiConfig['base_url'] ?? '';
        $this->mbPublicKey = $apiConfig['mb_public_key'] ?? '';
        $this->autoPayPublicKey = $apiConfig['autopay_public_key'] ?? '';
        $this->autoPayPrivateKey = $apiConfig['autopay_private_key'] ?? '';
        $this->authToken = $apiConfig['auth_token'] ?? '';
    }

    /**
     * Check if account number is valid and get account information
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): array
    {
        try {
            // MBBank account validation through VA account endpoint
            $endpoint = $this->baseUrl.'/va-account';

            // Create signature for the account number
            $signature = $this->createSignature($accountNumber);

            $response = Http::withHeaders([
                'signature' => $signature,
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'customerAcc' => $accountNumber,
            ]);

            if ($response->failed()) {
                Log::warning('MBBank account validation failed', [
                    'account_number' => $accountNumber,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình kết nối với ngân hàng MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyResponseSignature($responseData)) {
                Log::warning('MBBank response signature verification failed', [
                    'account_number' => $accountNumber,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank account validation successful', [
                    'account_number' => $accountNumber,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Thông tin tài khoản MBBank hợp lệ.',
                    'account_name' => $data['customerName'] ?? null,
                    'account_number' => $accountNumber,
                    'data' => $responseData,
                ];
            }

            return [
                'success' => false,
                'message' => $data['responseDesc'] ?? 'Tài khoản không hợp lệ.',
                'error_code' => 'INVALID_ACCOUNT',
            ];

        } catch (Exception $e) {
            Log::error('MBBank account validation error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình kiểm tra tài khoản MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Register account for banking services
     */
    public function registerAccount(array $accountData): array
    {
        try {
            // MBBank registration through token generation
            $endpoint = $this->baseUrl.'/token-generate';

            $authToken = $this->config['api_config']['auth_token'] ?? '';

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$authToken,
                'Content-Type' => 'application/json',
            ])->post($endpoint);

            if ($response->failed()) {
                Log::error('MBBank registration failed', [
                    'account_data' => $accountData,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình đăng ký tài khoản MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            Log::info('MBBank registration successful', [
                'account_data' => $accountData,
                'response_data' => $responseData,
            ]);

            return [
                'success' => true,
                'message' => 'Đăng ký tài khoản MBBank thành công.',
                'data' => $responseData,
                'access_token' => $responseData['access_token'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('MBBank registration error', [
                'account_data' => $accountData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đăng ký tài khoản MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Get transaction history for an account
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): array
    {
        Log::info('MBBank transaction history requested', [
            'account_number' => $accountNumber,
            'filters' => $filters,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng lịch sử giao dịch MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
            'transactions' => [],
            'total_count' => 0,
        ];
    }

    /**
     * Create virtual account (if supported by bank)
     */
    public function createVirtualAccount(array $virtualAccountData): array
    {
        try {
            // MBBank virtual account creation through VA account endpoint
            $endpoint = $this->baseUrl.'/va-account';

            $customerAcc = $virtualAccountData['customer_acc'] ?? '';

            // Create signature for the customer account
            $signature = $this->createSignature($customerAcc);

            $response = Http::withHeaders([
                'signature' => $signature,
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'customerAcc' => $customerAcc,
            ]);

            if ($response->failed()) {
                Log::error('MBBank virtual account creation failed', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyResponseSignature($responseData)) {
                Log::warning('MBBank VA creation response signature verification failed', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank virtual account created successfully', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Tạo tài khoản ảo MBBank thành công.',
                    'data' => $responseData,
                ];
            }

            return [
                'success' => false,
                'message' => $data['responseDesc'] ?? 'Không thể tạo tài khoản ảo.',
                'error_code' => 'CREATION_FAILED',
            ];

        } catch (Exception $e) {
            Log::error('MBBank virtual account creation error', [
                'virtual_account_data' => $virtualAccountData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Delete/disable virtual account (if supported by bank)
     */
    public function deleteVirtualAccount(array $virtualAccountData): array
    {
        Log::info('MBBank virtual account deletion requested', [
            'virtual_account_data' => $virtualAccountData,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xóa tài khoản ảo MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Get virtual account transactions
     */
    public function getVirtualAccountTransactions(array $filters = []): array
    {
        try {
            // MBBank transaction sync endpoint
            $endpoint = $this->baseUrl.'/va-transaction-sync';

            $requestData = [
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'referenceNumber' => $filters['reference_number'] ?? '',
                    'amount' => $filters['amount'] ?? 0,
                    'customerAcc' => $filters['customer_acc'] ?? '',
                    'transDate' => $filters['trans_date'] ?? now()->format('Y-m-d H:i:s'),
                    'billNumber' => $filters['bill_number'] ?? '',
                    'endPointUrl' => $filters['endpoint_url'] ?? null,
                    'userName' => $filters['user_name'] ?? null,
                    'rate' => $filters['rate'] ?? null,
                    'customerName' => $filters['customer_name'] ?? null,
                    'additionalData' => $filters['additional_data'] ?? [],
                ],
            ];

            // Create signature for transaction data
            $signatureData = $requestData['data']['referenceNumber'].
                           $requestData['data']['customerAcc'].
                           $requestData['data']['amount'].
                           $requestData['data']['transDate'];

            $signature = $this->createSignature($signatureData);
            $requestData['signature'] = $signature;

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post($endpoint, $requestData);

            if ($response->failed()) {
                Log::error('MBBank virtual account transactions failed', [
                    'filters' => $filters,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo MBBank.',
                    'error_code' => 'API_ERROR',
                    'transactions' => [],
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyTransactionResponseSignature($responseData)) {
                Log::warning('MBBank transaction response signature verification failed', [
                    'filters' => $filters,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi giao dịch từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                    'transactions' => [],
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank virtual account transactions retrieved successfully', [
                    'filters' => $filters,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Lấy giao dịch tài khoản ảo MBBank thành công.',
                    'transactions' => [$data], // MBBank returns single transaction
                    'data' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $data['responseDesc'] ?? 'Không thể lấy giao dịch.',
                    'error_code' => 'TRANSACTION_FAILED',
                    'transactions' => [],
                ];
            }

        } catch (Exception $e) {
            Log::error('MBBank virtual account transactions error', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo MBBank.',
                'error_code' => 'INTERNAL_ERROR',
                'transactions' => [],
            ];
        }
    }

    /**
     * Link account to banking services
     */
    public function linkAccount(string $accountNumber): array
    {
        Log::info('MBBank account linking requested', [
            'account_number' => $accountNumber,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng liên kết tài khoản MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Verify linked account with OTP
     */
    public function verifyLinkedAccount(array $verificationData): array
    {
        Log::info('MBBank account verification requested', [
            'verification_data' => $verificationData,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xác thực tài khoản MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Get account balance (if supported by bank)
     */
    public function getAccountBalance(string $accountNumber): ?float
    {
        Log::info('MBBank balance check requested', [
            'account_number' => $accountNumber,
        ]);

        // MBBank supports balance check according to config, but not implemented yet
        return null;
    }

    /**
     * Check if bank supports specific feature
     */
    public function supportsFeature(string $feature): bool
    {
        $supportedFeatures = $this->config['features'] ?? [];

        return in_array($feature, $supportedFeatures);
    }

    /**
     * Get bank configuration
     */
    public function getBankConfig(): array
    {
        return $this->config;
    }

    /**
     * Create RSA signature for data
     */
    protected function createSignature(string $data): string
    {
        openssl_sign($data, $signature, $this->autoPayPrivateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }

    /**
     * Verify RSA signature from MBBank
     */
    protected function verifySignature(string $data, string $signature): bool
    {
        $verify = openssl_verify($data, base64_decode($signature), $this->mbPublicKey, OPENSSL_ALGO_SHA256);

        return $verify === 1;
    }

    /**
     * Verify response signature from MBBank VA account endpoint
     */
    protected function verifyResponseSignature(array $responseData): bool
    {
        if (! isset($responseData['signature']) || ! isset($responseData['data']['customerAcc'])) {
            return false;
        }

        return $this->verifySignature(
            $responseData['data']['customerAcc'],
            $responseData['signature']
        );
    }

    /**
     * Verify transaction response signature from MBBank
     */
    protected function verifyTransactionResponseSignature(array $responseData): bool
    {
        if (! isset($responseData['signature']) || ! isset($responseData['data'])) {
            return false;
        }

        $data = $responseData['data'];
        $signatureData = ($data['transactionId'] ?? '').($data['responseCode'] ?? '');

        return $this->verifySignature($signatureData, $responseData['signature']);
    }

    /**
     * Sync transaction with MBBank
     */
    public function syncTransaction(array $transactionData): array
    {
        try {
            $endpoint = $this->baseUrl.'/transaction-sync';

            $authToken = $this->config['api_config']['auth_token'] ?? '';

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$authToken,
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'transactionid' => $transactionData['transaction_id'] ?? '',
            ]);

            if ($response->failed()) {
                Log::error('MBBank transaction sync failed', [
                    'transaction_data' => $transactionData,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            Log::info('MBBank transaction sync successful', [
                'transaction_data' => $transactionData,
                'response_data' => $responseData,
            ]);

            return [
                'success' => true,
                'message' => 'Đồng bộ giao dịch MBBank thành công.',
                'data' => $responseData,
            ];

        } catch (Exception $e) {
            Log::error('MBBank transaction sync error', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Authenticate with MBBank and get access token
     */
    public function authenticate(): ?string
    {
        try {
            $credentials = config('bank.supported_banks.mb.api_config');

            if (! $credentials['auth_token']) {
                Log::warning('Missing MBBank auth token in config');

                return null;
            }

            // MBBank uses static auth token from config
            $token = $credentials['auth_token'];

            if ($token) {
                // Save token to BankSettings with long expiration (MBBank tokens are typically long-lived)
                $expiresAt = now()->addDays(30)->toDateTimeString();
                $this->bankSettings->setAccessToken('mb', $token, $expiresAt);

                Log::info('MBBank authentication successful');

                return $token;
            }

            Log::warning('MBBank authentication failed - no token in config');

            return null;
        } catch (Exception $e) {
            Log::error('MBBank authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(): ?string
    {
        // MBBank doesn't support refresh tokens, re-authenticate instead
        Log::info('MBBank refresh token requested, re-authenticating instead');

        return $this->authenticate();
    }

    /**
     * Get valid access token (authenticate if needed)
     */
    public function getAccessToken(): ?string
    {
        $token = $this->bankSettings->getAccessToken('mb');

        if (! $token || $this->bankSettings->isTokenExpired('mb')) {
            $token = $this->authenticate();
        }

        return $token;
    }

    /**
     * Make authenticated HTTP request to bank API
     */
    public function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        $token = $this->getAccessToken();

        if (! $token) {
            return [
                'success' => false,
                'message' => 'Unable to get valid access token',
                'error_code' => 'AUTH_FAILED',
            ];
        }

        // Get bank-specific API URL
        $apiUrl = config('bank.supported_banks.mb.api_config.base_url');
        if (! $apiUrl) {
            return [
                'success' => false,
                'message' => 'Bank API URL not configured',
                'error_code' => 'CONFIG_ERROR',
            ];
        }

        $url = rtrim($apiUrl, '/').'/'.ltrim($endpoint, '/');

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Content-Type' => 'application/json',
            ])->timeout(30)
                ->{strtolower($method)}($url, $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status' => $response->status(),
                ];
            }

            // If unauthorized, try to refresh token and retry once
            if ($response->status() === 401) {
                Log::info('MBBank token expired, attempting to re-authenticate');

                $newToken = $this->authenticate();

                if ($newToken) {
                    $retryResponse = Http::withHeaders([
                        'Authorization' => 'Bearer '.$newToken,
                        'Content-Type' => 'application/json',
                    ])->timeout(30)
                        ->{strtolower($method)}($url, $data);

                    if ($retryResponse->successful()) {
                        return [
                            'success' => true,
                            'data' => $retryResponse->json(),
                            'status' => $retryResponse->status(),
                        ];
                    }
                }
            }

            return [
                'success' => false,
                'message' => 'API request failed',
                'error_code' => 'API_ERROR',
                'status' => $response->status(),
                'response' => $response->body(),
            ];
        } catch (Exception $e) {
            Log::error('Error making authenticated request to MBBank', [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint,
            ]);

            return [
                'success' => false,
                'message' => 'Request failed due to network error',
                'error_code' => 'NETWORK_ERROR',
            ];
        }
    }

    /**
     * Test bank connection
     */
    public function testConnection(): array
    {
        try {
            $token = $this->getAccessToken();

            if (! $token) {
                return [
                    'success' => false,
                    'message' => 'Unable to authenticate with MBBank',
                    'error_code' => 'AUTH_FAILED',
                ];
            }

            // Try to make a simple API call to test connection
            $result = $this->makeAuthenticatedRequest('GET', '/health');

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'MBBank connection successful',
                    'token_expires_at' => $this->bankSettings->getTokenExpiration('mb'),
                ];
            }

            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'MBBank connection test failed: '.$e->getMessage(),
                'error_code' => 'CONNECTION_FAILED',
            ];
        }
    }

    /**
     * Clear all tokens for MBBank
     */
    public function clearTokens(): void
    {
        $this->bankSettings->clearTokens('mb');
        Log::info('Cleared all tokens for MBBank');
    }

    /**
     * Get token status for MBBank
     */
    public function getTokenStatus(): array
    {
        $token = $this->bankSettings->getAccessToken('mb');
        $expiresAt = $this->bankSettings->getTokenExpiration('mb');
        $requestedAt = $this->bankSettings->getTokenRequestedAt('mb');
        $isExpired = $this->bankSettings->isTokenExpired('mb');

        return [
            'has_token' => ! empty($token),
            'expires_at' => $expiresAt,
            'requested_at' => $requestedAt,
            'is_expired' => $isExpired,
            'expires_in_minutes' => $expiresAt ? Carbon::parse($expiresAt)->diffInMinutes(now()) : null,
            'age_in_minutes' => $requestedAt ? Carbon::parse($requestedAt)->diffInMinutes(now()) : null,
        ];
    }

    /**
     * Generate access token
     */
    public function generateToken(): array
    {
        try {
            Log::info('MBBank token generation requested');

            return [
                'success' => true,
                'access_token' => $this->authToken,
                'token_type' => 'bearer',
                'expires_in' => 59,
            ];

        } catch (Exception $e) {
            Log::error('MBBank token generation error', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo token MBBank.',
                'error_code' => 'TOKEN_ERROR',
            ];
        }
    }

    /**
     * Validate virtual account
     */
    public function validateVirtualAccount(string $customerAcc): array
    {
        try {
            // Verify signature
            $verify = $this->verifyCustomerAccount($customerAcc);

            if ($verify) {
                $signature = $this->createSignature($customerAcc);

                return [
                    'success' => true,
                    'requestId' => strtolower(Str::ulid()),
                    'data' => [
                        'customerAcc' => $customerAcc,
                        'customerName' => 'AUTOPAY',
                        'responseCode' => '00',
                        'responseDesc' => 'Thành công',
                    ],
                    'signature' => $signature,
                ];
            }

            return [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'customerAcc' => $customerAcc,
                    'customerName' => 'Không tìm thấy khách hàng',
                    'responseDesc' => 'Không tìm thấy khách hàng',
                    'responseCode' => '01',
                ],
                'signature' => $this->createSignature($customerAcc),
            ];

        } catch (Exception $e) {
            Log::error('MBBank VA validation error', [
                'customer_acc' => $customerAcc,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xác thực tài khoản ảo MBBank.',
                'error_code' => 'VALIDATION_ERROR',
            ];
        }
    }

    /**
     * Sync virtual account transaction
     */
    public function syncVirtualAccountTransaction(array $transactionData): array
    {
        try {
            $signatureData = $transactionData['referenceNumber'].
                           $transactionData['customerAcc'].
                           $transactionData['amount'].
                           $transactionData['transDate'];

            $verify = $this->verifySignature($signatureData, $transactionData['signature']);
            $transactionId = strtolower(Str::ulid());

            if ($verify) {
                $responseCode = '00';
                $signature = $this->createTransactionSignature($transactionId.$responseCode);

                return [
                    'success' => true,
                    'requestId' => strtolower(Str::ulid()),
                    'data' => [
                        'transactionId' => $transactionId,
                        'responseCode' => $responseCode,
                        'responseDesc' => 'Successful',
                    ],
                    'signature' => $signature,
                ];
            }

            $responseCode = '01';
            $signature = $this->createTransactionSignature($transactionId.$responseCode);

            return [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'transactionId' => $transactionId,
                    'responseCode' => $responseCode,
                    'responseDesc' => 'Failed',
                ],
                'signature' => $signature,
            ];

        } catch (Exception $e) {
            Log::error('MBBank VA transaction sync error', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                'error_code' => 'SYNC_ERROR',
            ];
        }
    }

    /**
     * Verify customer account signature
     */
    protected function verifyCustomerAccount(string $customerAcc): bool
    {
        // In real implementation, this would verify against MBBank signature
        // For now, return true for valid format
        return strlen($customerAcc) >= 8 && strlen($customerAcc) <= 19;
    }

    /**
     * Create transaction signature
     */
    protected function createTransactionSignature(string $data): string
    {
        openssl_sign($data, $signature, $this->autoPayPrivateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }
}
