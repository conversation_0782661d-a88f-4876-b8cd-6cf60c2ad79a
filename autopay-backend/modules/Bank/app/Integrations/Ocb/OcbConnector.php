<?php

namespace Modules\Bank\Integrations\Ocb;

use Illuminate\Support\Carbon;
use JsonException;
use Modules\Bank\Integrations\Ocb\Requests\AccessTokenRequest;
use Modules\Core\Helpers\ResponseHelper;
use App\Settings\BankSettings;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Http\Auth\TokenAuthenticator;
use Saloon\Http\Connector;
use Saloon\Http\Response;
use Saloon\Traits\Plugins\AcceptsJson;

class OcbConnector extends Connector
{
    use AcceptsJson;

    public ?int $tries = 2;

    // Checking for requesting class to avoid infinite looping for access token request class
    private ?string $currentRequestClass = null;

    protected BankSettings $settings;

    public function __construct()
    {
        $this->settings = app(BankSettings::class);
    }

    public function resolveBaseUrl(): string
    {
        return config('bank.supported_banks.ocb.api_config.base_url', 'https://api.ocb.com.vn/corporates/partner');
    }

    public function hasRequestFailed(Response $response): ?bool
    {
        // Check if the response is a 401 Unauthorized
        if ($response->status() === 401) {
            $this->settings->clearTokens('ocb');
        }

        return $response->status() >= 500;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    public function getAccessToken(): string
    {
        $token = $this->settings->getAccessToken('ocb');

        if (!$token || $this->settings->isTokenExpired('ocb')) {
            $this->currentRequestClass = AccessTokenRequest::class;
            $response = $this->send(new AccessTokenRequest);
            $this->currentRequestClass = null;

            if ($response->failed()) {
                abort(ResponseHelper::error('Không thể gửi yêu cầu lấy access token với ngân hàng, vui lòng liên hệ với bộ phận hỗ trợ.', 400));
            }

            $responseData = $response->array();
            $token = $responseData['access_token'];
            $expiresAt = now()->addSeconds($responseData['expires_in'] - 60)->toDateTimeString();

            $this->settings->setAccessToken('ocb', $token, $expiresAt);
        }

        return $token;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    protected function defaultAuth(): TokenAuthenticator
    {
        return new TokenAuthenticator($this->currentRequestClass !== AccessTokenRequest::class ? $this->getAccessToken() : '');
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'X-Client-Certificate' => config('bank.supported_banks.ocb.api_config.client_certificate'),
            'X-IBM-Client-Id' => config('bank.supported_banks.ocb.api_config.client_id'),
            'X-IBM-Client-Secret' => config('bank.supported_banks.ocb.api_config.client_secret'),
        ];

    }
}
