<?php

namespace Modules\Bank\Integrations\Ocb\Requests;

use Modules\Bank\Integrations\Ocb\OcbBaseRequest;
use App\Settings\BankSettings;
use Illuminate\Support\Str;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class VerifyMerchantInfoRequest extends OcbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    protected BankSettings $settings;

    public function __construct(public array $merchantInfo)
    {
        $this->settings = app(BankSettings::class);
    }

    public function resolveEndpoint(): string
    {
        return '/v1/merchant/individual/verify-info';
    }

    protected function defaultHeaders(): array
    {
        return [
            'X-Signature' => $this->generateSignature($this->defaultBody()),
        ];
    }

    protected function defaultBody(): array
    {
        return once(function () {
            return [
                'trace' => [
                    'clientTransId' => Str::of(Str::ulid())->lower()->toString(),
                    'clientTimestamp' => date('YmdHis000'),
                ],
                'data' => [
                    'merchantInfo' => $this->merchantInfo,
                ],
            ];
        });
    }
}
