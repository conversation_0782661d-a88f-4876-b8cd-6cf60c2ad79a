<?php

namespace Modules\Bank\Integrations\Klb\Requests;

use Modules\Bank\Integrations\Klb\KlbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class CheckAccountNumberRequest extends KlbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public string $accountNumber)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/api/openBanking/v1/checkAccountNo';
    }

    protected function defaultHeaders(): array
    {
        $timestamp = $this->generateTimestamp();
        $payload = json_encode(['accountNo' => $this->accountNumber]);
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);
        
        $clientId = config('bank.supported_banks.klb.api_config.client_id');
        $secretKey = config('bank.supported_banks.klb.api_config.secret_key');
        $apiValidate = $this->generateApiValidate($clientId, $timestamp, $encryptedData, $secretKey);

        return [
            'Content-Type' => 'application/json',
            'x-api-client' => $clientId,
            'x-api-time' => (string) $timestamp,
            'x-api-validate' => $apiValidate,
        ];
    }

    protected function defaultBody(): array
    {
        $payload = json_encode(['accountNo' => $this->accountNumber]);
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);

        return [
            'data' => $encryptedData,
        ];
    }
}
