<?php

namespace Modules\Bank\Integrations\Klb\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class AccessTokenRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function resolveEndpoint(): string
    {
        return '/token-generate';
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'client_id' => config('bank.supported_banks.klb.api_config.client_id'),
            'client_secret' => config('bank.supported_banks.klb.api_config.client_secret'),
        ];
    }
}
