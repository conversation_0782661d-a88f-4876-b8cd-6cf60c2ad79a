<?php

namespace Modules\Bank\Integrations\Klb\Requests;

use Modules\Bank\Integrations\Klb\KlbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class CreateVirtualAccountRequest extends KlbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public array $virtualAccountData)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/api/payment/v1/virtualAccount/enable';
    }

    protected function defaultHeaders(): array
    {
        $timestamp = $this->generateTimestamp();
        $payload = json_encode([
            'order' => $this->virtualAccountData['order'] ?? 1,
            'timeout' => $this->virtualAccountData['timeout'] ?? 0,
            'fixAmount' => $this->virtualAccountData['fix_amount'] ?? 0,
            'fixContent' => $this->virtualAccountData['fix_content'] ?? 'donate',
            'bankAccountNo' => $this->virtualAccountData['bank_account_no'] ?? '',
        ]);
        
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);
        
        $clientId = config('bank.supported_banks.klb.api_config.client_id');
        $secretKey = config('bank.supported_banks.klb.api_config.secret_key');
        $apiValidate = $this->generateApiValidate($clientId, $timestamp, $encryptedData, $secretKey);

        return [
            'Content-Type' => 'application/json',
            'x-api-client' => $clientId,
            'x-api-time' => (string) $timestamp,
            'x-api-validate' => $apiValidate,
        ];
    }

    protected function defaultBody(): array
    {
        $payload = json_encode([
            'order' => $this->virtualAccountData['order'] ?? 1,
            'timeout' => $this->virtualAccountData['timeout'] ?? 0,
            'fixAmount' => $this->virtualAccountData['fix_amount'] ?? 0,
            'fixContent' => $this->virtualAccountData['fix_content'] ?? 'donate',
            'bankAccountNo' => $this->virtualAccountData['bank_account_no'] ?? '',
        ]);
        
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);

        return [
            'data' => $encryptedData,
        ];
    }
}
