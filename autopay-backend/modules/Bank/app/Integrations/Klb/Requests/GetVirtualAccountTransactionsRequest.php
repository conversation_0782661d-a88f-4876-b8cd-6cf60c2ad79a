<?php

namespace Modules\Bank\Integrations\Klb\Requests;

use Modules\Bank\Integrations\Klb\KlbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class GetVirtualAccountTransactionsRequest extends KlbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public array $filters)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/api/payment/v1/getTransaction';
    }

    protected function defaultHeaders(): array
    {
        $timestamp = $this->generateTimestamp();
        $payload = json_encode([
            'order' => $this->filters['order'] ?? 1,
            'page' => $this->filters['page'] ?? 0,
            'size' => $this->filters['size'] ?? 500,
            'bankAccountNo' => $this->filters['bank_account_no'] ?? '',
            'fromDate' => $this->filters['from_date'] ?? date('Y-m-d 00:00:00'),
            'toDate' => $this->filters['to_date'] ?? date('Y-m-d 23:59:59'),
        ]);
        
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);
        
        $clientId = config('bank.supported_banks.klb.api_config.client_id');
        $secretKey = config('bank.supported_banks.klb.api_config.secret_key');
        $apiValidate = $this->generateApiValidate($clientId, $timestamp, $encryptedData, $secretKey);

        return [
            'Content-Type' => 'application/json',
            'x-api-client' => $clientId,
            'x-api-time' => (string) $timestamp,
            'x-api-validate' => $apiValidate,
        ];
    }

    protected function defaultBody(): array
    {
        $payload = json_encode([
            'order' => $this->filters['order'] ?? 1,
            'page' => $this->filters['page'] ?? 0,
            'size' => $this->filters['size'] ?? 500,
            'bankAccountNo' => $this->filters['bank_account_no'] ?? '',
            'fromDate' => $this->filters['from_date'] ?? date('Y-m-d 00:00:00'),
            'toDate' => $this->filters['to_date'] ?? date('Y-m-d 23:59:59'),
        ]);
        
        $encryptKey = config('bank.supported_banks.klb.api_config.encrypt_key');
        $encryptedData = $this->encryptAES($payload, $encryptKey);

        return [
            'data' => $encryptedData,
        ];
    }
}
