<?php

namespace Modules\Bank\Integrations\Klb;

use Saloon\Http\Request;

abstract class KlbBaseRequest extends Request
{
    /**
     * Encrypt data using AES-256-CBC
     */
    protected function encryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $pri_key = hex2bin($key);
        $data_utf8 = mb_convert_encoding($data, 'UTF-8');
        $encrypted_data = openssl_encrypt(
            $data_utf8,
            'aes-256-cbc',
            $pri_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($encrypted_data);
    }

    /**
     * Decrypt data using AES-256-CBC
     */
    protected function decryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $key = hex2bin($key);

        return openssl_decrypt(
            base64_decode($data),
            'aes-256-cbc',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }

    /**
     * Generate HMAC signature
     */
    protected function hmacEncode(string $data, string $key): string
    {
        $hmac = hash_hmac('sha256', $data, $key, true);

        return bin2hex($hmac);
    }

    /**
     * Generate timestamp for KLB API
     */
    protected function generateTimestamp(): int
    {
        return time() * 1000;
    }

    /**
     * Generate API validation signature
     */
    protected function generateApiValidate(string $clientId, int $timestamp, string $encryptedData, string $secretKey): string
    {
        return $this->hmacEncode("{$clientId}|{$timestamp}|{$encryptedData}", $secretKey);
    }
}
