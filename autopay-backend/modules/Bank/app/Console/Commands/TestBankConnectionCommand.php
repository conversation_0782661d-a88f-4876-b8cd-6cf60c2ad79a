<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use App\Settings\BankSettings;
use Modules\Bank\Contracts\BankStrategyInterface;

class TestBankConnectionCommand extends Command
{
    protected $signature = 'bank:test-connection {bank? : Bank code to test (ocb, mb, klb)}';

    protected $description = 'Test bank API connection and authentication';

    public function handle(): int
    {
        $bankCode = $this->argument('bank');
        $settings = app(BankSettings::class);

        if ($bankCode) {
            return $this->testSingleBank($bankCode, $settings);
        }

        // Test all banks
        $banks = ['ocb', 'mb', 'klb'];
        $results = [];

        foreach ($banks as $bank) {
            $results[$bank] = $this->testBankConnection($bank, $settings);
        }

        $this->displayResults($results);

        return Command::SUCCESS;
    }

    protected function testSingleBank(string $bankCode, BankSettings $settings): int
    {
        $this->info("Testing connection for {$bankCode} bank...");

        $result = $this->testBankConnection($bankCode, $settings);

        if ($result['success']) {
            $this->info("✅ {$bankCode}: {$result['message']}");
            if (isset($result['token_expires_at'])) {
                $this->line("   Token expires at: {$result['token_expires_at']}");
            }
        } else {
            $this->error("❌ {$bankCode}: {$result['message']}");
            if (isset($result['error_code'])) {
                $this->line("   Error code: {$result['error_code']}");
            }
        }

        return $result['success'] ? Command::SUCCESS : Command::FAILURE;
    }

    protected function testBankConnection(string $bankCode, BankSettings $settings): array
    {
        try {
            // Get bank strategy
            $bankConfig = config("bank.supported_banks.{$bankCode}");
            if (!$bankConfig || !isset($bankConfig['strategy_class'])) {
                return [
                    'success' => false,
                    'message' => "Bank {$bankCode} not configured or strategy class not found",
                    'error_code' => 'BANK_NOT_CONFIGURED'
                ];
            }

            $strategyClass = $bankConfig['strategy_class'];
            if (!class_exists($strategyClass)) {
                return [
                    'success' => false,
                    'message' => "Strategy class {$strategyClass} not found",
                    'error_code' => 'STRATEGY_CLASS_NOT_FOUND'
                ];
            }

            /** @var BankStrategyInterface $strategy */
            $strategy = new $strategyClass($bankConfig);

            // Test connection using strategy
            return $strategy->testConnection();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'error_code' => 'CONNECTION_ERROR'
            ];
        }
    }

    protected function displayResults(array $results): void
    {
        $this->info('Bank Connection Test Results:');
        $this->line('');

        $headers = ['Bank', 'Status', 'Message', 'Token Expires'];
        $rows = [];

        foreach ($results as $bank => $result) {
            $status = $result['success'] ? '✅ Success' : '❌ Failed';
            $message = $result['message'];
            $expires = $result['token_expires_at'] ?? 'N/A';

            $rows[] = [
                strtoupper($bank),
                $status,
                $message,
                $expires
            ];
        }

        $this->table($headers, $rows);

        $successCount = count(array_filter($results, fn($r) => $r['success']));
        $totalCount = count($results);

        $this->line('');
        $this->info("Summary: {$successCount}/{$totalCount} banks connected successfully");
    }
}
