<?php

namespace Modules\Bank\Contracts;



/**
 * Bank Strategy Interface
 *
 * Defines the contract for all bank integration strategies.
 * Each bank implementation must implement this interface.
 */
interface BankStrategyInterface
{
    /**
     * Check if account number is valid and get account information
     *
     * @param string $accountNumber The account number to validate
     * @param array $additionalData Additional data required by specific bank (phone, ID card, etc.)
     * @return array ['success' => bool, 'message' => string, 'account_name' => string, 'account_number' => string, 'data' => mixed, 'error_code' => string]
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): array;

    /**
     * Register account for banking services
     *
     * @param array $accountData Account registration data
     * @return array ['success' => bool, 'message' => string, 'data' => mixed, 'access_token' => string, 'error_code' => string]
     */
    public function registerAccount(array $accountData): array;

    /**
     * Get transaction history for an account
     *
     * @param string $accountNumber The account number
     * @param array $filters Filters for transaction history (date range, limit, etc.)
     * @return array ['success' => bool, 'message' => string, 'transactions' => array, 'total_count' => int, 'data' => mixed, 'error_code' => string]
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): array;

    /**
     * Create virtual account (if supported by bank)
     * Returns array with success/error status and data
     *
     * @param array $virtualAccountData Data for virtual account creation
     * @return array ['success' => bool, 'message' => string, 'data' => mixed, 'error_code' => string]
     */
    public function createVirtualAccount(array $virtualAccountData): array;

    /**
     * Delete/disable virtual account (if supported by bank)
     * Returns array with success/error status and data
     *
     * @param array $virtualAccountData Data for virtual account deletion
     * @return array ['success' => bool, 'message' => string, 'error_code' => string]
     */
    public function deleteVirtualAccount(array $virtualAccountData): array;

    /**
     * Get virtual account transactions
     * Returns array with success/error status and transactions data
     *
     * @param array $filters Filters for VA transaction history
     * @return array ['success' => bool, 'message' => string, 'transactions' => array, 'data' => mixed, 'error_code' => string]
     */
    public function getVirtualAccountTransactions(array $filters = []): array;

    /**
     * Link account to banking services
     * Returns array with success/error status and session data
     *
     * @param string $accountNumber The account number to link
     * @return array ['success' => bool, 'message' => string, 'session_id' => string, 'data' => mixed, 'error_code' => string]
     */
    public function linkAccount(string $accountNumber): array;

    /**
     * Verify linked account with OTP
     * Returns array with success/error status and verification data
     *
     * @param array $verificationData Verification data (sessionId, otp, accountNumber)
     * @return array ['success' => bool, 'message' => string, 'data' => mixed, 'error_code' => string]
     */
    public function verifyLinkedAccount(array $verificationData): array;

    /**
     * Get account balance (if supported by bank)
     *
     * @param string $accountNumber The account number
     * @return float|null
     */
    public function getAccountBalance(string $accountNumber): ?float;

    /**
     * Check if bank supports specific feature
     *
     * @param string $feature Feature name (virtual_account, balance_check, transaction_history, etc.)
     * @return bool
     */
    public function supportsFeature(string $feature): bool;

    /**
     * Get bank configuration
     *
     * @return array
     */
    public function getBankConfig(): array;

    /**
     * Authenticate with bank and get access token
     *
     * @return string|null
     */
    public function authenticate(): ?string;

    /**
     * Refresh access token using refresh token
     *
     * @return string|null
     */
    public function refreshToken(): ?string;

    /**
     * Get valid access token (authenticate if needed)
     *
     * @return string|null
     */
    public function getAccessToken(): ?string;

    /**
     * Make authenticated HTTP request to bank API
     * Returns array with success/error status and response data
     *
     * @param string $method HTTP method (GET, POST, etc.)
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array ['success' => bool, 'message' => string, 'data' => mixed, 'status' => int, 'error_code' => string]
     */
    public function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array;

    /**
     * Test bank connection
     * Returns array with success/error status and connection info
     *
     * @return array ['success' => bool, 'message' => string, 'token_expires_at' => string, 'error_code' => string]
     */
    public function testConnection(): array;

    /**
     * Clear all tokens for this bank
     *
     * @return void
     */
    public function clearTokens(): void;

    /**
     * Get token status for this bank
     *
     * @return array
     */
    public function getTokenStatus(): array;

    /**
     * Sync transaction with bank (optional method for banks that support it)
     * Returns array with success/error status and sync result
     *
     * @param array $transactionData Transaction data to sync
     * @return array ['success' => bool, 'message' => string, 'data' => mixed, 'error_code' => string]
     */
    public function syncTransaction(array $transactionData): array;
}
