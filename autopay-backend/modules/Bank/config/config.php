<?php

return [
    'name' => 'Bank',
    /*
    |--------------------------------------------------------------------------
    | Supported Banks Configuration
    |--------------------------------------------------------------------------
    |
    | This array contains configuration for all supported banks.
    | Each bank must have a strategy class that implements BankStrategyInterface.
    |
    */
    'supported_banks' => [
        'ocb' => [
            'name' => 'OceanBank',
            'display_name' => 'Ngân hàng TMCP Phương Đông (OCB)',
            'strategy_class' => \Modules\Bank\Strategies\OcbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber', 'idCardNumber', 'phoneNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'transaction_history',
                'transaction_sync',
                'virtual_account_transactions',
                'account_linking',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => false,
                'transaction_history' => true,
                'account_validation' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
                'account_linking' => true,
            ],
            'api_config' => [
                'username' => env('OCB_USERNAME'),
                'password' => env('OCB_PASSWORD'),
                'client_id' => env('OCB_CLIENT_ID'),
                'client_secret' => env('OCB_CLIENT_SECRET'),
                'virtual_account_prefix' => env('OCB_VIRTUAL_ACCOUNT_PREFIX'),
                'api_url' => env('OCB_API_URL', 'https://api.ocb.com.vn'),
            ],
        ],

        'mb' => [
            'name' => 'MBBank',
            'display_name' => 'Ngân hàng TMCP Quân đội (MBBank)',
            'strategy_class' => \Modules\Bank\Strategies\MbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber', 'accountName', 'idCardNumber', 'phoneNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'transaction_history',
                'balance_check',
                'transaction_sync',
                'virtual_account_transactions',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => true,
                'transaction_history' => true,
                'account_validation' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
            ],
            'api_config' => [
                'username' => env('MB_USERNAME'),
                'password' => env('MB_PASSWORD'),
                'virtual_account_prefix' => env('MB_VIRTUAL_ACCOUNT_PREFIX'),
                'api_url' => env('MB_API_URL', 'https://api.mbbank.com.vn'),
            ],
        ],

        'klb' => [
            'name' => 'KienLongBank',
            'display_name' => 'Ngân hàng TMCP Kiên Long',
            'strategy_class' => \Modules\Bank\Strategies\KlbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'account_linking',
                'virtual_account_transactions',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => false,
                'transaction_history' => false,
                'account_validation' => true,
                'account_linking' => true,
                'virtual_account_transactions' => true,
            ],
            'api_config' => [
                'base_url' => env('KLB_BASE_URL', 'https://api.kienlongbank.co/pay'),
                'client_id' => env('KLB_CLIENT_ID'),
                'client_secret' => env('KLB_CLIENT_SECRET'),
                'secret_key' => env('KLB_SECRET_KEY'),
                'encrypt_key' => env('KLB_ENCRYPT_KEY'),
                'virtual_account_prefix' => env('KLB_VIRTUAL_ACCOUNT_PREFIX'),
            ],
        ],
    ],
];
